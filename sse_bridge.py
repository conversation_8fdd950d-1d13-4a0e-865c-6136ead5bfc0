#!/usr/bin/env python3
"""
SSE Event Bridge System
Bridges HTTP authentication events with SSE communication for Claude Desktop
"""

import asyncio
import json
import uuid
from datetime import datetime
from typing import Dict, Any, Optional
import logging

logger = logging.getLogger(__name__)

class SSEEventBridge:
    """Bridge between HTTP authentication and SSE communication"""
    
    def __init__(self):
        # Event queues for each session
        self.session_queues: Dict[str, asyncio.Queue] = {}
        # Session data storage
        self.sessions: Dict[str, Dict[str, Any]] = {}
        # Global event queue for broadcast events
        self.global_queue = asyncio.Queue()
        
    def create_session(self, session_id: str = None) -> str:
        """Create a new SSE session"""
        if not session_id:
            session_id = str(uuid.uuid4())
            
        self.session_queues[session_id] = asyncio.Queue()
        self.sessions[session_id] = {
            "created_at": datetime.now().isoformat(),
            "last_activity": datetime.now().isoformat(),
            "authenticated": False
        }
        
        logger.info(f"📡 Created SSE session: {session_id}")
        return session_id
    
    def remove_session(self, session_id: str):
        """Remove SSE session and cleanup"""
        if session_id in self.session_queues:
            del self.session_queues[session_id]
        if session_id in self.sessions:
            del self.sessions[session_id]
        logger.info(f"🗑️ Removed SSE session: {session_id}")
    
    async def send_to_session(self, session_id: str, event_data: Dict[str, Any]):
        """Send event to specific session"""
        if session_id in self.session_queues:
            await self.session_queues[session_id].put(event_data)
            self.sessions[session_id]["last_activity"] = datetime.now().isoformat()
            logger.debug(f"📤 Sent event to session {session_id}: {event_data.get('type', 'unknown')}")
    
    async def send_to_all_sessions(self, event_data: Dict[str, Any]):
        """Send event to all active sessions"""
        for session_id in list(self.session_queues.keys()):
            await self.send_to_session(session_id, event_data)
    
    async def get_session_events(self, session_id: str):
        """Get events for a specific session (async generator for SSE)"""
        if session_id not in self.session_queues:
            self.create_session(session_id)
            
        queue = self.session_queues[session_id]
        
        # Send initial connection event
        await self.send_to_session(session_id, {
            "type": "connection",
            "data": {
                "session_id": session_id,
                "status": "connected",
                "timestamp": datetime.now().isoformat()
            }
        })
        
        try:
            while True:
                # Wait for events with timeout
                try:
                    event = await asyncio.wait_for(queue.get(), timeout=30.0)
                    yield event
                except asyncio.TimeoutError:
                    # Send keepalive
                    yield {
                        "type": "keepalive",
                        "data": {"timestamp": datetime.now().isoformat()}
                    }
        except asyncio.CancelledError:
            logger.info(f"🔌 SSE session {session_id} disconnected")
            self.remove_session(session_id)
            raise
    
    # Authentication event methods (called by HTTP callback server)
    def notify_auth_required(self, session_id: str = None):
        """Notify that authentication is required"""
        event = {
            "type": "auth_required",
            "data": {
                "status": "authentication_required",
                "message": "Please authenticate with Kite Connect",
                "timestamp": datetime.now().isoformat()
            }
        }
        
        if session_id:
            asyncio.create_task(self.send_to_session(session_id, event))
        else:
            asyncio.create_task(self.send_to_all_sessions(event))
    
    def notify_auth_complete(self, session_id: str = None):
        """Notify that authentication is complete"""
        event = {
            "type": "auth_complete",
            "data": {
                "status": "authenticated",
                "message": "Authentication successful - ready to trade",
                "timestamp": datetime.now().isoformat()
            }
        }
        
        # Update session status
        if session_id and session_id in self.sessions:
            self.sessions[session_id]["authenticated"] = True
        
        if session_id:
            asyncio.create_task(self.send_to_session(session_id, event))
        else:
            asyncio.create_task(self.send_to_all_sessions(event))
    
    def notify_auth_expired(self, session_id: str = None):
        """Notify that authentication has expired"""
        event = {
            "type": "auth_expired",
            "data": {
                "status": "expired",
                "message": "Authentication expired - please re-authenticate",
                "timestamp": datetime.now().isoformat()
            }
        }
        
        # Update session status
        if session_id and session_id in self.sessions:
            self.sessions[session_id]["authenticated"] = False
        
        if session_id:
            asyncio.create_task(self.send_to_session(session_id, event))
        else:
            asyncio.create_task(self.send_to_all_sessions(event))
    
    def notify_trade_complete(self, session_id: str, trade_data: Dict[str, Any]):
        """Notify that a trade has been completed"""
        event = {
            "type": "trade_complete",
            "data": {
                "status": "success",
                "trade_data": trade_data,
                "timestamp": datetime.now().isoformat()
            }
        }
        
        asyncio.create_task(self.send_to_session(session_id, event))
    
    def get_session_info(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get session information"""
        return self.sessions.get(session_id)
    
    def get_all_sessions(self) -> Dict[str, Dict[str, Any]]:
        """Get all session information"""
        return self.sessions.copy()

# Global SSE bridge instance
sse_bridge = SSEEventBridge()
